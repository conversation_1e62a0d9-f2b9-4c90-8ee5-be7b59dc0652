## Context:
We are pleased to announce our decision to develop a website for hosting cyber and information security training videos. The core objective is to create an interactive platform where users can access training content, engage with assessments, and obtain certifications. Below are the detailed requirements:

## 1. Multilingual Video Integration
- The website should host two training videos in different languages (e.g., English and Chinese). If additional languages are added in the future, the system should be scalable. The questions and answers for the assessments should be in the same language as the video, again switchable from the button however user will 
not be able to switch languages after starting the viewing or quiz and will have to rewatch video.
- Implement a user-friendly language selection feature (e.g., a dropdown menu) to allow users to switch between videos seamlessly.
## 2. Interactive Training Structure
- Video Viewing: Each language version should have a dedicated page with embedded video players.
- Assessment System:
    - Include a set of questions (e.g., multiple-choice or true/false) for users to complete after watching the video.
    - Set a passing threshold of 70% accuracy (i.e., users must answer at least 70% of questions correctly to progress).
- Certificate Generation:
    - Upon passing, users should receive a digital certificate featuring:
    - Training title (e.g., "Cybersecurity Fundamentals Training"),
    - User name,
    - Completion date.
## 3. Data Tracking and Reporting
- Develop a backend system to record and store user completion data, including:
    - Video language selected,
    - Quiz scores,
    - Completion status (passed/failed),
    - Timestamp of completion.
- Provide an administrative dashboard or exportable report for reviewing aggregate training metrics.

## 4. SOC report generation
- Ability to export a report on user completion status for each training module.
- Observe standard for SOC report requirements
- Exported report should be in a HTML file format for the administrator to view.

### Frontend: 
- Framework - React
- UI Library - Ant Design
- Video Player - React Player
- Auth - Simple login page, no need fancy jwt as it is for internal use only

### Backend:
- NodeJS
- Express.js
- Authentication - simple auth email + pw
- REST API

### Storage:
- Azure CDN for video storage

### Database:
- PostgresSQL

### Containerize:
- Docker

DB uses user email as primary key

User accounts are generated by the system with input being email address, imported as a string of text, each email address seprated by adding a ";"

User accounts generation takes the email as an input with previous formatting, then for each email will generate a random password. The administrator will need to manually copy the password and send it to the user so just include it inside the user list with a button to copy the password.

User must not be able to skip forward in the video, however allowed to rewind. 

After the training video upload is complete for a new quiz, the administrator will use the web UI system to create the quiz, the quiz creation system should be similar to google forms however stripped down and has question formats that dont have wildcard answers, so things like MC and TF questions. Perhaps add short answer question capability but hide that under a warning saying that it will not be graded.

Use external port 8390 and localhost/127.0.0.1

Modulate code as coin order to make sure no code is redundant at extreme degrees.


